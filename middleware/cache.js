const { getCacheConfig } = require("../config/cache-config");
const redisClient = require("../utils/redis-client");

/**
 * Cache middleware for handling GET request caching
 */
function cacheMiddleware() {
  return async (req, res, next) => {
    // Only cache GET requests
    if (req.method !== "GET") {
      return next();
    }

    // Extract path without query parameters
    const path = req.path;
    const cacheConfig = getCacheConfig(path);

    // If path is not configured for caching, skip
    if (!cacheConfig) {
      console.log(
        `Path ${path} not configured for caching, skipping cache middleware`
      );
      return next();
    }

    console.log(
      `Path ${path} is configured for caching with TTL: ${cacheConfig.ttl}s`
    );

    // Extract query parameters for cache key generation
    const queryParams = req.query || {};
    const cacheKey = redisClient.generateCacheKey(path, queryParams);

    try {
      // Try to get cached response
      const cachedResponse = await redisClient.get(cacheKey);

      if (cachedResponse) {
        console.log(`Cache HIT for key: ${cacheKey}`);

        // Parse cached response
        const parsedResponse = JSON.parse(cachedResponse);

        // Set headers from cached response
        if (parsedResponse.headers) {
          Object.keys(parsedResponse.headers).forEach((header) => {
            res.set(header, parsedResponse.headers[header]);
          });
        }

        // Add cache hit header
        res.set("X-Cache", "HIT");
        res.set("X-Cache-TTL", cacheConfig.ttl.toString());
        res.set("X-Cache-Key", cacheKey);

        // Return cached response
        return res
          .status(parsedResponse.status || 200)
          .json(parsedResponse.data);
      }

      console.log(`Cache MISS for key: ${cacheKey}`);

      // Store original res.json method
      const originalJson = res.json;
      const originalStatus = res.status;
      let responseStatus = 200;

      // Override res.status to capture status code
      res.status = function (code) {
        responseStatus = code;
        return originalStatus.call(this, code);
      };

      // Override res.json to cache the response
      res.json = function (data) {
        // Only cache successful responses (2xx status codes)
        if (responseStatus >= 200 && responseStatus < 300) {
          const responseToCache = {
            status: responseStatus,
            data: data,
            headers: {
              "content-type": "application/json",
              "x-cache": "MISS",
              "x-cache-ttl": cacheConfig.ttl.toString(),
            },
            timestamp: new Date().toISOString(),
          };

          // Cache the response asynchronously
          redisClient
            .set(cacheKey, JSON.stringify(responseToCache), cacheConfig.ttl)
            .then(() => {
              console.log(
                `Cached response for key: ${cacheKey} with TTL: ${cacheConfig.ttl}s`
              );
            })
            .catch((err) => {
              console.error(
                `Failed to cache response for key: ${cacheKey}`,
                err
              );
            });
        }

        // Add cache miss headers
        res.set("X-Cache", "MISS");
        res.set("X-Cache-TTL", cacheConfig.ttl.toString());
        res.set("X-Cache-Key", cacheKey);

        // Call original json method
        return originalJson.call(this, data);
      };

      // Continue to next middleware
      next();
    } catch (error) {
      console.error("Cache middleware error:", error);
      // Continue without caching on error
      next();
    }
  };
}

module.exports = cacheMiddleware;
