const redis = require("redis");

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  /**
   * Initialize Redis connection
   */
  async connect() {
    try {
      this.client = redis.createClient({
        socket: {
          host: process.env.REDIS_HOST || "127.0.0.1",
          port: Number(process.env.REDIS_PORT) || 6379,
        },
        password: process.env.REDIS_PASSWORD || undefined,
        database: Number(process.env.REDIS_DB) || 0,
      });

      this.client.on("error", (err) => {
        console.error("Redis Client Error:", err);
        this.isConnected = false;
      });

      this.client.on("connect", () => {
        console.log("Connected to Redis");
      });

      this.client.on("ready", () => {
        console.log("Redis client ready");
        this.isConnected = true;
      });

      this.client.on("end", () => {
        console.log("Redis connection ended");
        this.isConnected = false;
      });

      await this.client.connect();
      return true;
    } catch (error) {
      console.error("Failed to connect to Redis:", error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Get value from Redis
   * @param {string} key - Cache key
   * @returns {Promise<string|null>} - Cached value or null
   */
  async get(key) {
    if (!this.isConnected || !this.client) {
      console.warn("Redis not connected, skipping cache get");
      return null;
    }

    try {
      return await this.client.get(key);
    } catch (error) {
      console.error("Redis GET error:", error);
      return null;
    }
  }

  /**
   * Set value in Redis with TTL
   * @param {string} key - Cache key
   * @param {string} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} - Success status
   */
  async set(key, value, ttl) {
    if (!this.isConnected || !this.client) {
      console.warn("Redis not connected, skipping cache set");
      return false;
    }

    try {
      await this.client.setEx(key, ttl, value);
      return true;
    } catch (error) {
      console.error("Redis SET error:", error);
      return false;
    }
  }

  /**
   * Delete key from Redis
   * @param {string} key - Cache key to delete
   * @returns {Promise<boolean>} - Success status
   */
  async del(key) {
    if (!this.isConnected || !this.client) {
      console.warn("Redis not connected, skipping cache delete");
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error("Redis DEL error:", error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async disconnect() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
    }
  }

  /**
   * Generate cache key for request including query parameters
   * @param {string} path - Request path
   * @param {object} queryParams - Query parameters object (optional)
   * @returns {string} - Cache key
   */
  generateCacheKey(path, queryParams = {}) {
    // Start with the base path
    let cacheKey = `seeker-cache:${path}`;

    // If there are query parameters, add them to the cache key
    if (queryParams && Object.keys(queryParams).length > 0) {
      const normalizedQuery = this._normalizeQueryParams(queryParams);
      if (normalizedQuery) {
        cacheKey += `?${normalizedQuery}`;
      }
    }

    return cacheKey;
  }

  /**
   * Normalize query parameters for consistent cache key generation
   * @param {object} queryParams - Query parameters object
   * @returns {string} - Normalized query string
   * @private
   */
  _normalizeQueryParams(queryParams) {
    if (!queryParams || typeof queryParams !== "object") {
      return "";
    }

    // Convert query params to array of key-value pairs
    const pairs = [];

    Object.keys(queryParams).forEach((key) => {
      const value = queryParams[key];

      // Handle array values (e.g., ?tags=bar&tags=restaurant)
      if (Array.isArray(value)) {
        value.forEach((val) => {
          pairs.push([key, val]);
        });
      } else if (value !== undefined && value !== null) {
        pairs.push([key, value]);
      }
    });

    // Sort pairs by key first, then by value for consistent ordering
    pairs.sort((a, b) => {
      if (a[0] === b[0]) {
        return String(a[1]).localeCompare(String(b[1]));
      }
      return a[0].localeCompare(b[0]);
    });

    // Create normalized query string with proper URL encoding
    return pairs
      .map(([key, value]) => {
        const encodedKey = encodeURIComponent(key);
        const encodedValue = encodeURIComponent(String(value));
        return `${encodedKey}=${encodedValue}`;
      })
      .join("&");
  }
}

// Create singleton instance
const redisClient = new RedisClient();

module.exports = redisClient;
